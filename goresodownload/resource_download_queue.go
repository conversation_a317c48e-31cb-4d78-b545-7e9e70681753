package goresodownload

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	DOWNLOAD_ALLOWED_MS       = 15 * 60 * 1000 // 15 min
	DEFAULT_DOWNLOAD_START_TS = "1970-01-01T00:00:00Z"
)

// QueueItem represents an item in the download queue
type QueueItem struct {
	ID           string    `bson:"_id"`
	Src          string    `bson:"src"`
	Mt           time.Time `bson:"_mt"`
	DlShallEndTs time.Time `bson:"dlShallEndTs"`
	Priority     int       `bson:"priority"`
}

// ResourceDownloadQueue manages the download queue for resources
type ResourceDownloadQueue struct {
	queueCol *gomongo.MongoCollection
}

// NewResourceDownloadQueue creates a new ResourceDownloadQueue instance
func NewResourceDownloadQueue(queueCol *gomongo.MongoCollection) (*ResourceDownloadQueue, error) {
	if queueCol == nil {
		return nil, fmt.Errorf("collection cannot be nil")
	}

	queue := &ResourceDownloadQueue{
		queueCol: queueCol,
	}

	// Ensure indexes
	if err := queue.ensureIndexes(); err != nil {
		return nil, fmt.Errorf("failed to ensure indexes: %w", err)
	}

	return queue, nil
}

// ensureIndexes creates necessary indexes for the queue collection
func (q *ResourceDownloadQueue) ensureIndexes() error {
	ctx := context.Background()

	// Create compound index on src, priority and dlShallEndTs for optimal query performance
	// Order: src (filter) -> priority (sort) -> dlShallEndTs (filter)
	// This allows MongoDB to efficiently use the index for both filtering and sorting
	indexModel := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "src", Value: 1},
				{Key: "priority", Value: -1},
				{Key: "dlShallEndTs", Value: 1},
			},
		},
	}

	_, err := q.queueCol.CreateIndexes(ctx, indexModel)
	if err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	return nil
}

// AddToQueue adds a resource to the download queue
func (q *ResourceDownloadQueue) AddToQueue(id string, priority int, src string) error {
	// Parse default download start time
	defaultStartTime, err := time.Parse(time.RFC3339, DEFAULT_DOWNLOAD_START_TS)
	if err != nil {
		return fmt.Errorf("failed to parse default start time: %w", err)
	}

	// Use id as _id (primary key)
	ctx := context.Background()
	filter := bson.M{"_id": id}

	// Create update document without changeDoc
	updateDoc := bson.M{
		"_id":          id,
		"src":          src,
		"_mt":          time.Now(),
		"dlShallEndTs": defaultStartTime,
		"priority":     priority,
	}
	update := bson.M{"$set": updateDoc}
	opts := options.Update().SetUpsert(true)

	_, err = q.queueCol.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to add to queue: %w", err)
	}

	return nil
}

// GetNewPropFromWatchTable retrieves the full document from the appropriate watch table
func GetNewPropFromWatchTable(id, boardType string) (bson.M, error) {
	watchTable := BoardWatchTable[boardType]
	if watchTable == "" {
		return nil, fmt.Errorf("unknown board type: %s", boardType)
	}

	watchCol := gomongo.Coll("rni", watchTable)
	var result bson.M
	ctx := context.Background()
	err := watchCol.FindOne(ctx, bson.M{"_id": id}).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("failed to find document in watch table %s: %w", watchTable, err)
	}

	return result, nil
}

// GetNextBatch gets the next batch of resources to download for a specific board
// Loops batchSize times calling GetNext to get individual items
func (q *ResourceDownloadQueue) GetNextBatch(boardType string, batchSize int) ([]QueueItem, error) {
	if batchSize <= 0 {
		batchSize = 100
	}

	var results []QueueItem

	for i := 0; i < batchSize; i++ {
		item, err := q.GetNext(boardType)
		if err != nil {
			return results, fmt.Errorf("failed to get next item at index %d: %w", i, err)
		}

		if item == nil {
			// No more items available, break the loop
			break
		}

		results = append(results, *item)
	}

	return results, nil
}

// GetNext gets the next single resource to download for a specific board
// Uses findOneAndUpdate to atomically find and lock the next item
func (q *ResourceDownloadQueue) GetNext(boardType string) (*QueueItem, error) {
	ctx := context.Background()

	// Filter: items with dlShallEndTs in the past and matching boardType
	filter := bson.M{
		"src":          boardType,
		"dlShallEndTs": bson.M{"$lt": time.Now()},
	}

	// Update: set dlShallEndTs to current time + DOWNLOAD_ALLOWED_MS
	newDlShallEndTs := time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)
	update := bson.M{
		"$set": bson.M{
			"dlShallEndTs": newDlShallEndTs,
		},
	}

	// Options: sort by priority descending (highest priority first)
	opts := options.FindOneAndUpdate().
		SetSort(bson.M{"priority": -1}).
		SetReturnDocument(options.After) // Return the updated document

	var result QueueItem
	err := q.queueCol.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // No items available
		}
		return nil, fmt.Errorf("failed to find and update queue item: %w", err)
	}

	return &result, nil
}

// RemoveFromQueue removes a resource from the queue
func (q *ResourceDownloadQueue) RemoveFromQueue(item *QueueItem) error {
	ctx := context.Background()

	filter := bson.M{"_id": item.ID}
	_, err := q.queueCol.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to remove from queue: %w", err)
	}

	return nil
}

// RemoveBatchFromQueue removes multiple resources from the queue
func (q *ResourceDownloadQueue) RemoveBatchFromQueue(items []QueueItem) error {
	if len(items) == 0 {
		return nil
	}

	ctx := context.Background()

	// Build filter for batch deletion using _id
	var ids []string
	for _, item := range items {
		ids = append(ids, item.ID)
	}

	filter := bson.M{"_id": bson.M{"$in": ids}}
	_, err := q.queueCol.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to remove batch from queue: %w", err)
	}

	return nil
}
