package main

import (
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"

	levelStore "github.com/real-rm/golevelstore"
)

var (
	intToBase62Flag   = flag.String("int", "", "Convert integer to base62 (e.g., -int=123456)")
	base62ToIntFlag   = flag.String("base62", "", "Convert base62 to integer (e.g., -base62=W7E)")
	batchFlag         = flag.Bool("batch", false, "Batch mode: read from stdin, one value per line")
	helpFlag          = flag.Bool("help", false, "Show help message")
)

func main() {
	flag.Parse()

	if *helpFlag {
		showHelp()
		return
	}

	// Batch mode
	if *batchFlag {
		runBatchMode()
		return
	}

	// Single conversion mode
	if *intToBase62Flag != "" && *base62ToIntFlag != "" {
		fmt.Println("Error: Cannot specify both -int and -base62 at the same time")
		os.Exit(1)
	}

	if *intToBase62Flag != "" {
		convertIntToBase62(*intToBase62Flag)
		return
	}

	if *base62ToIntFlag != "" {
		convertBase62ToInt(*base62ToIntFlag)
		return
	}

	// No flags specified, show help
	showHelp()
}

func showHelp() {
	fmt.Println("Base62 Converter Tool")
	fmt.Println("====================")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  Convert integer to base62:")
	fmt.Println("    ./base62converter -int=123456")
	fmt.Println()
	fmt.Println("  Convert base62 to integer:")
	fmt.Println("    ./base62converter -base62=W7E")
	fmt.Println()
	fmt.Println("  Batch mode (read from stdin):")
	fmt.Println("    echo -e '123456\\n789012' | ./base62converter -batch")
	fmt.Println("    echo -e 'W7E\\nBcDe' | ./base62converter -batch")
	fmt.Println()
	fmt.Println("Flags:")
	fmt.Println("  -int=VALUE      Convert integer to base62")
	fmt.Println("  -base62=VALUE   Convert base62 to integer")
	fmt.Println("  -batch          Batch mode: read from stdin")
	fmt.Println("  -help           Show this help message")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  ./base62converter -int=123456")
	fmt.Println("  ./base62converter -base62=W7E")
	fmt.Println("  ./base62converter -int=-107903121")
	fmt.Println("  ./base62converter -base62=someBase62String")
}

func convertIntToBase62(intStr string) {
	// Parse the integer
	intVal, err := strconv.ParseInt(intStr, 10, 32)
	if err != nil {
		fmt.Printf("Error: Invalid integer '%s': %v\n", intStr, err)
		os.Exit(1)
	}

	// Convert to base62
	base62, err := levelStore.Int32ToBase62(int32(intVal))
	if err != nil {
		fmt.Printf("Error: Failed to convert %d to base62: %v\n", intVal, err)
		os.Exit(1)
	}

	fmt.Printf("Integer: %d\n", intVal)
	fmt.Printf("Base62:  %s\n", base62)
}

func convertBase62ToInt(base62Str string) {
	// Convert from base62
	intVal, err := levelStore.Base62ToInt32(base62Str)
	if err != nil {
		fmt.Printf("Error: Failed to convert '%s' from base62: %v\n", base62Str, err)
		os.Exit(1)
	}

	fmt.Printf("Base62:  %s\n", base62Str)
	fmt.Printf("Integer: %d\n", intVal)
}

func runBatchMode() {
	fmt.Println("Batch mode: Enter values (one per line), press Ctrl+D to finish:")
	fmt.Println("Format: 'int:123456' or 'base62:W7E' or just the value (auto-detect)")
	fmt.Println()

	var input string
	lineNum := 1

	for {
		fmt.Printf("Line %d: ", lineNum)
		n, err := fmt.Scanln(&input)
		if err != nil || n == 0 {
			break
		}

		processBatchLine(input, lineNum)
		lineNum++
		fmt.Println()
	}

	fmt.Println("Batch processing completed.")
}

func processBatchLine(input string, lineNum int) {
	input = strings.TrimSpace(input)
	if input == "" {
		return
	}

	// Check for explicit format
	if strings.HasPrefix(input, "int:") {
		intStr := strings.TrimPrefix(input, "int:")
		fmt.Printf("Converting integer: %s\n", intStr)
		convertIntToBase62(intStr)
		return
	}

	if strings.HasPrefix(input, "base62:") {
		base62Str := strings.TrimPrefix(input, "base62:")
		fmt.Printf("Converting base62: %s\n", base62Str)
		convertBase62ToInt(base62Str)
		return
	}

	// Auto-detect: try to parse as integer first
	if intVal, err := strconv.ParseInt(input, 10, 32); err == nil {
		fmt.Printf("Auto-detected as integer: %s\n", input)
		convertIntToBase62(input)
		return
	}

	// If not an integer, treat as base62
	fmt.Printf("Auto-detected as base62: %s\n", input)
	convertBase62ToInt(input)
}
