# 需求 [fix_phoMedia]

## 反馈

1. ********* 图片url 404
2. DOWNLOAD_ALLOWED_MS 原来30s太短，会出现多次被处理
prodRni [direct: primary] rni>  db.reso_photo_download_queue.find({_id:'TRB*********'})
  {
    _id: 'TRB*********',
    _mt: ISODate('2025-07-24T16:35:34.216Z'),
    _ts: ISODate('2025-07-24T16:35:33.907Z'),
    dlShallEndTs: ISODate('2025-07-24T16:36:04.216Z'),
    priority: 50000,
    src: 'TRB'
  }
  [
  {
    _id: 'TRB*********',
    _mt: ISODate('2025-07-24T16:36:04.319Z'),
    _ts: ISODate('2025-07-24T16:35:33.907Z'),
    dlShallEndTs: ISODate('2025-07-24T16:36:34.319Z'),
    priority: 50000,
    src: 'TRB'
  }
]

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-24

## 原因
1.********* 处理queue的时候，显示没有media，获取到了当时replaceOne 没有media的情况。
TRBN12298969出现删除了一半图片的情况，需要batch处理

## 解决办法
1. 修改addToQueue，支持 根据phoUrls解压后 长度和phoLH长度不一致的，添加到queue
2. 修改addToQueue，支持 check phoLH,docLH,tnLH对应的图片是否都在本地存在，不存在的话，删除之前的phoLH字段，添加到queue
3. 重构addToQueue
4. 增加DOWNLOAD_ALLOWED_MS 到 5 min



## 是否需要补充UT

1. 添加

## 确认日期:    2025-07-24


## online-step

1. 运行 batch，将phoUrls解压后 长度和phoLH长度不一致的文档添加到队列
   ```bash

 ./start.sh -t batch -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go  -board=TRB -phoNum -dryrun"

   ```

2. 运行 batch，check本地图片，添加到queue
   ```bash

 ./start.sh -t batch -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go  -board=TRB -checkLocal -dryrun"

   ```
