# addToQueue -phoNum 功能使用说明

## 功能描述

新增的 `-phoNum` 参数用于检查 MongoDB 中 `phoUrls` 字段解压后的长度与 `phoLH` 字段长度是否一致。如果长度不一致，则将该文档添加到下载队列中进行重新处理。

## 使用方法

### 基本用法

```bash
# 干运行模式 - 只检查不实际添加到队列
./start.sh -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -phoNum -dryrun"

# 实际运行 - 将长度不匹配的文档添加到队列
./start.sh -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -phoNum"
```

### 参数说明

- `-phoNum`: 启用 phoUrls 长度检查模式
- `-board=TRB`: 指定要处理的板块（通常是 TRB）
- `-dryrun`: 干运行模式，只记录日志不实际执行

## 工作原理

1. **查询文档**: 查询所有包含 `phoUrls` 字段的文档
2. **解压 phoUrls**: 使用 gzip 解压 MongoDB BinData 类型的 `phoUrls` 字段
3. **解析 JSON**: 将解压后的数据解析为 JSON 数组
4. **计算长度**: 获取解压后数组的长度
5. **比较长度**: 与 `phoLH` 字段的长度进行比较
6. **添加到队列**: 如果长度不一致，将文档 ID 添加到下载队列

## 日志输出

### 成功找到长度不匹配的文档
```
INFO Length mismatch found - adding to queue _id=TRBC12204262 phoUrls_length=5 phoLH_length=3
INFO Successfully added to queue _id=TRBC12204262 board=TRB priority=50000
```

### 长度匹配的文档（跳过）
```
DEBUG Lengths match - skipping document _id=TRBC12204263 phoUrls_length=3 phoLH_length=3
```

### 错误情况
```
ERROR Failed to decompress phoUrls _id=TRBC12204264 error="failed to create gzip reader: gzip: invalid header"
```

## 统计信息

程序会输出以下统计信息：
- `processed`: 处理的文档总数
- `length_mismatch_found`: 发现长度不匹配的文档数
- `skipped_lengths_match`: 长度匹配被跳过的文档数
- `skipped_no_phoUrls`: 没有 phoUrls 字段的文档数
- `phoUrls_decompress_errors`: 解压失败的文档数
- `added`: 成功添加到队列的文档数

## 注意事项

1. **数据格式**: `phoUrls` 字段必须是 MongoDB BinData 类型的 gzip 压缩 JSON 数据
2. **内存使用**: 解压大量数据时可能消耗较多内存
3. **性能**: 对于大量文档，建议先使用 `-dryrun` 模式评估影响
4. **错误处理**: 解压失败的文档会被记录但不会中断整个处理过程

## 故障排除

### 常见错误

1. **"phoUrls is not a primitive.Binary type"**
   - 原因: phoUrls 字段不是 BinData 类型
   - 解决: 检查数据库中的字段类型

2. **"failed to create gzip reader: gzip: invalid header"**
   - 原因: 数据不是有效的 gzip 格式
   - 解决: 检查数据是否正确压缩

3. **"failed to parse decompressed JSON"**
   - 原因: 解压后的数据不是有效的 JSON
   - 解决: 检查原始数据格式

## 示例场景

针对需求文档中提到的 C12204262 问题：

```bash
# 检查特定文档
./start.sh -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -id=TRBC12204262 -dryrun"

# 批量检查所有 TRB 文档的 phoUrls 长度
./start.sh -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -phoNum -dryrun"
```
