# 需求 [handle_old_data]

## 反馈

1. 目前goresodownload图片下载只处理了20250714开始的数据，需要处理旧的数据

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-14

## 原因
1. 旧数据也需要按照现在目录结构存储

## 解决办法
1. 旧的reso图片，直接batch添加到现在的reso_photo_download_queue中。
2. 旧的rets图片，需要迁移 TRB，DDF，CLG，OTW
  a. useTrebOnlinePhoto before2018-01-01
  b. useOldLogic


  ddf:   /#{ddfID.substr(-3)}/#{ddfID.substr(3)}_#{num}.jpg"
   /mnt/ca6m0/mlsimgs/crea/ddf/img/174/23214174_1.jpg, num: [1..prop.pho]



3. RM listing? genRMListingPicUrls   picUrls: [ 'http://f.realmaster.cn/P/JQUN/CPM.jpg' ]
4. 


后续：
1. crebdownload 修改为使用L1/L2

## 是否需要补充UT

1. 已补充

## 确认日期:    2025-07-17

## online-step

1. 重启goresodownload
  ```
  systemctl --user stop  batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
  ```
2. 运行batch
```
./start.sh  -n goAddPhoP -d "goresodownload" -cmd "cmd/batch/AddPhoP.go -dryrun"
```
dryrun:[processed(30196):19.55K/m dryrun(30196):19.55K/m]